import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'core/theme/app_theme.dart';
import 'core/services/api_service.dart';
import 'core/services/storage_service.dart';
import 'features/home/<USER>';
import 'features/home/<USER>/lesson_provider.dart';
import 'features/lesson_detail/providers/lesson_detail_provider.dart';
import 'features/lesson_detail/providers/quiz_provider.dart';
import 'features/lesson_detail/providers/ai_exercise_provider.dart';
import 'features/auth/providers/auth_provider.dart';
import 'features/payment/payment_provider.dart';

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Get application documents directory
    final appDocumentDir = await getApplicationDocumentsDirectory();

    // Initialize Hive with the documents directory path
    Hive.init(appDocumentDir.path);

    // Initialize the StorageService
    await StorageService.instance.init();

    print('Storage initialization completed successfully');
  } catch (e) {
    print('Error during storage initialization: $e');
    // Continue with app launch even if storage fails
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<LessonProvider>(
          create: (context) => LessonProvider(
            ApiService(),
            StorageService.instance,
          ),
        ),
        ChangeNotifierProvider<LessonDetailProvider>(
          create: (context) => LessonDetailProvider(ApiService()),
        ),
        ChangeNotifierProvider<QuizProvider>(
          create: (context) => QuizProvider(
            ApiService(),
            StorageService.instance,
          ),
        ),
        ChangeNotifierProvider<AiExerciseProvider>(
          create: (context) => AiExerciseProvider(ApiService()),
        ),
        ChangeNotifierProvider<AuthProvider>(
          create: (context) => AuthProvider(
            ApiService(),
            StorageService.instance,
          ),
        ),
        ChangeNotifierProvider<PaymentProvider>(
          create: (context) => PaymentProvider(
            ApiService(),
            StorageService.instance,
          ),
        ),
      ],
      child: MaterialApp(
        title: 'نویسنده شو',
        theme: AppTheme.darkTheme(),
        debugShowCheckedModeBanner: false,
        locale: const Locale('fa', 'IR'),
        supportedLocales: const [
          Locale('fa', 'IR'),
        ],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        home: const HomeScreen(),
      ),
    );
  }
}
