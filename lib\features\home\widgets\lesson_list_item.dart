import 'package:flutter/material.dart';
import '../models/lesson_model.dart';
import '../../lesson_detail/lesson_detail_screen.dart';
import '../../../core/theme/app_theme.dart';

class LessonListItem extends StatelessWidget {
  final Lesson lesson;
  final VoidCallback? onTap;

  const LessonListItem({
    super.key,
    required this.lesson,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 6,
        child: ListTile(
          enabled: lesson.isFree,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 12,
          ),
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: lesson.isFree
                  ? AppTheme.kSuccessColor.withValues(alpha: 0.15)
                  : AppTheme.kLockedColor.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              lesson.isFree ? Icons.play_circle_fill : Icons.lock,
              color: lesson.isFree
                  ? AppTheme.kSuccessColor
                  : AppTheme.kLockedColor,
              size: 28,
            ),
          ),
          title: Text(
            lesson.title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: lesson.isFree
                      ? AppTheme.kTextColor
                      : AppTheme.kTextColor.withValues(alpha: 0.8),
                  fontWeight: FontWeight.w600,
                ),
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.right,
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Icon(
                  lesson.isFree ? Icons.check_circle : Icons.star,
                  size: 16,
                  color: lesson.isFree
                      ? AppTheme.kSuccessColor
                      : AppTheme.kLockedColor,
                ),
                const SizedBox(width: 4),
                Text(
                  lesson.isFree ? 'درس رایگان' : 'درس ویژه',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: lesson.isFree
                            ? AppTheme.kSuccessColor
                            : AppTheme.kLockedColor,
                        fontWeight: FontWeight.w500,
                      ),
                  textDirection: TextDirection.rtl,
                ),
              ],
            ),
          ),
          trailing: _buildTrailingWidget(),
          onTap: lesson.isFree
              ? () => _navigateToLessonDetail(context)
              : () => _showLockedLessonDialog(context),
        ),
      ),
    );
  }

  /// Build the trailing widget based on lesson status
  Widget _buildTrailingWidget() {
    if (!lesson.isFree) {
      // Locked lesson
      return Icon(
        Icons.lock_outline,
        color: AppTheme.kLockedColor.withValues(alpha: 0.7),
        size: 16,
      );
    } else if (lesson.isCompleted) {
      // Completed free lesson
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.check_circle,
            color: AppTheme.kSuccessColor,
            size: 20,
          ),
          const SizedBox(width: 4),
          Icon(
            Icons.arrow_forward_ios,
            color: AppTheme.kSuccessColor.withValues(alpha: 0.7),
            size: 14,
          ),
        ],
      );
    } else {
      // Available free lesson (not completed)
      return Icon(
        Icons.arrow_forward_ios,
        color: AppTheme.kSuccessColor,
        size: 16,
      );
    }
  }

  void _navigateToLessonDetail(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LessonDetailScreen(lessonId: lesson.id),
      ),
    );
  }

  void _showLockedLessonDialog(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'برای دسترسی به این درس، ابتدا باید اشتراک تهیه کنید',
          style: const TextStyle(fontFamily: 'Samim'),
          textDirection: TextDirection.rtl,
        ),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
