import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/auth_provider.dart';

/// Authentication screen for user login/registration via mobile OTP
/// Handles two stages: mobile number entry and OTP verification
class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  // Controllers for managing text input
  late TextEditingController _mobileController;
  late TextEditingController _otpController;

  // State management variables
  bool _isOtpSent =
      false; // Toggle between mobile input and OTP verification stages
  String?
      _enteredMobile; // Stores validated mobile number after successful OTP sending

  @override
  void initState() {
    super.initState();
    // Initialize text controllers
    _mobileController = TextEditingController();
    _otpController = TextEditingController();
  }

  @override
  void dispose() {
    // Clean up controllers to prevent memory leaks
    _mobileController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  /// Sends OTP to the entered mobile number
  /// Uses AuthProvider to make actual API call
  Future<void> _sendOtp() async {
    // Validate mobile number input
    if (_mobileController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لطفاً شماره موبایل خود را وارد کنید'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Get provider instance
    final provider = Provider.of<AuthProvider>(context, listen: false);

    // Call API through provider
    final result = await provider.sendOtp(_mobileController.text.trim());

    if (result) {
      // Success - switch to OTP verification stage and store validated mobile number
      setState(() {
        _isOtpSent = true;
        _enteredMobile =
            _mobileController.text.trim(); // Store validated mobile
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('کد تایید به شماره $_enteredMobile ارسال شد'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else {
      // Failure - show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خطا در ارسال کد تایید'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Verifies the entered OTP code
  /// Uses AuthProvider to make actual API call with stored mobile number
  Future<void> _verifyOtp() async {
    // Validate OTP input
    if (_otpController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لطفاً کد تایید را وارد کنید'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Get provider instance
    final provider = Provider.of<AuthProvider>(context, listen: false);

    // Call API through provider using stored mobile number
    final result = await provider.verifyOtp(
      _enteredMobile!, // Use stored mobile number instead of controller
      _otpController.text.trim(),
    );

    if (result) {
      // Success - show success message and navigate back
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('ورود موفقیت‌آمیز بود'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back to previous screen
        Navigator.of(context).pop();
      }
    } else {
      // Failure - show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('کد تایید نامعتبر است'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Builds the mobile number input stage UI
  Widget _buildMobileInputStage() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Mobile number input field
        TextField(
          controller: _mobileController,
          keyboardType: TextInputType.phone,
          textAlign: TextAlign.center,
          textDirection: TextDirection.ltr, // LTR for phone numbers
          decoration: const InputDecoration(
            labelText: 'شماره موبایل',
            border: OutlineInputBorder(),
            prefixText: '+98 ',
          ),
        ),
        const SizedBox(height: 20),
        // Send OTP button or loading indicator with provider state
        Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            return authProvider.isLoading
                ? const Center(child: CircularProgressIndicator())
                : ElevatedButton(
                    onPressed: _sendOtp,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('ارسال کد'),
                  );
          },
        ),
      ],
    );
  }

  /// Builds the OTP verification stage UI
  Widget _buildOtpVerificationStage() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Information text showing where OTP was sent (using stored mobile number)
        Text(
          'کد تایید به شماره $_enteredMobile ارسال شد',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 20),
        // OTP input field
        TextField(
          controller: _otpController,
          keyboardType: TextInputType.number,
          textAlign: TextAlign.center,
          maxLength: 6,
          decoration: const InputDecoration(
            labelText: 'کد تایید',
            border: OutlineInputBorder(),
            counterText: '', // Hide character counter
          ),
        ),
        const SizedBox(height: 20),
        // Verify OTP button or loading indicator with provider state
        Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            return authProvider.isLoading
                ? const Center(child: CircularProgressIndicator())
                : ElevatedButton(
                    onPressed: _verifyOtp,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('تایید کد'),
                  );
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // App bar with Persian title
      appBar: AppBar(
        title: const Text('ورود یا ثبت‌نام'),
        centerTitle: true,
      ),
      // Main body with padding
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _isOtpSent
            ? _buildOtpVerificationStage() // Show OTP verification stage
            : _buildMobileInputStage(), // Show mobile number input stage
      ),
    );
  }
}
