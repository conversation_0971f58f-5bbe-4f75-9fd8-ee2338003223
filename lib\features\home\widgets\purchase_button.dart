import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../payment/payment_provider.dart';
import '../../auth/providers/auth_provider.dart';
import '../../auth/auth_screen.dart';

/// Purchase button widget for buying the full version
/// Shows different states based on purchase status and loading state
/// Context-aware: checks authentication status and uses persisted mobile number
class PurchaseButton extends StatelessWidget {
  const PurchaseButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<PaymentProvider>(
      builder: (context, paymentProvider, child) {
        // If already purchased, show disabled button
        if (paymentProvider.isPurchased) {
          return _buildPurchasedButton(context);
        }

        // If loading, show progress indicator
        if (paymentProvider.isLoading) {
          return _buildLoadingButton(context);
        }

        // Show active purchase button
        return _buildPurchaseButton(context, paymentProvider);
      },
    );
  }

  /// Build button for already purchased state
  Widget _buildPurchasedButton(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: ElevatedButton.icon(
        onPressed: null, // Disabled
        icon: const Icon(Icons.check_circle, color: Colors.green),
        label: const Text(
          'خریداری شده',
          style: TextStyle(
            fontFamily: 'Samim',
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          textDirection: TextDirection.rtl,
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.grey[300],
          foregroundColor: Colors.grey[600],
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  /// Build loading button
  Widget _buildLoadingButton(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: ElevatedButton(
        onPressed: null, // Disabled during loading
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.tealAccent[400],
          foregroundColor: Colors.black,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'در حال پردازش...',
              style: TextStyle(
                fontFamily: 'Samim',
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
      ),
    );
  }

  /// Build active purchase button
  Widget _buildPurchaseButton(
    BuildContext context,
    PaymentProvider paymentProvider,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: ElevatedButton.icon(
        onPressed: () => _handlePurchase(context, paymentProvider),
        icon: const Icon(Icons.shopping_cart, color: Colors.black),
        label: const Text(
          'خرید نسخه کامل',
          style: TextStyle(
            fontFamily: 'Samim',
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          textDirection: TextDirection.rtl,
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.tealAccent[400],
          foregroundColor: Colors.black,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
      ),
    );
  }

  /// Handle purchase button press with authentication check
  void _handlePurchase(BuildContext context, PaymentProvider paymentProvider) {
    // Get AuthProvider instance to check authentication status
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // Check if user is authenticated
    if (authProvider.isLoggedIn) {
      // User is authenticated, proceed with purchase flow
      _proceedWithPurchase(context, paymentProvider, authProvider);
    } else {
      // User is not authenticated, redirect to authentication
      _redirectToAuthentication(context);
    }
  }

  /// Proceed with purchase for authenticated users
  void _proceedWithPurchase(
    BuildContext context,
    PaymentProvider paymentProvider,
    AuthProvider authProvider,
  ) {
    // Get the persisted mobile number
    final mobileNumber = authProvider.mobileNumber;

    // Validate mobile number is available
    if (mobileNumber == null || mobileNumber.isEmpty) {
      // Show error message if mobile number is not available
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'خطا: شماره موبایل یافت نشد. لطفاً مجدداً وارد شوید.',
            style: TextStyle(fontFamily: 'Samim'),
            textDirection: TextDirection.rtl,
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Show confirmation dialog before purchase
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'تایید خرید',
            style: TextStyle(fontFamily: 'Samim'),
            textDirection: TextDirection.rtl,
          ),
          content: const Text(
            'آیا مایل به خرید نسخه کامل هستید؟',
            style: TextStyle(fontFamily: 'Samim'),
            textDirection: TextDirection.rtl,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'انصراف',
                style: TextStyle(fontFamily: 'Samim'),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Start purchase process with authenticated user's mobile number
                try {
                  paymentProvider.purchaseProduct(mobileNumber);
                } catch (e) {
                  // Handle any errors during purchase initiation
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'خطا در شروع فرآیند خرید: ${e.toString()}',
                        style: const TextStyle(fontFamily: 'Samim'),
                        textDirection: TextDirection.rtl,
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.tealAccent[400],
                foregroundColor: Colors.black,
              ),
              child: const Text('خرید', style: TextStyle(fontFamily: 'Samim')),
            ),
          ],
        );
      },
    );
  }

  /// Redirect unauthenticated users to authentication screen
  void _redirectToAuthentication(BuildContext context) {
    // Show informative message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'برای خرید اشتراک، ابتدا باید وارد شوید',
          style: TextStyle(fontFamily: 'Samim'),
          textDirection: TextDirection.rtl,
        ),
        backgroundColor: Colors.red,
      ),
    );

    // Navigate to authentication screen
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AuthScreen()));
  }
}
