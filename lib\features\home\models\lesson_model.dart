class Lesson {
  final int id;
  final String title;
  final bool isFree;
  final String content;
  final bool isCompleted;

  Lesson({
    required this.id,
    required this.title,
    required this.isFree,
    required this.content,
    this.isCompleted = false,
  });

  factory Lesson.fromJson(Map<String, dynamic> json) {
    return Lesson(
      id: json['id'] as int? ?? 0,
      title: json['title'] as String? ?? '',
      isFree: json['is_free'] as bool? ?? false,
      content: json['content'] as String? ?? '',
      isCompleted: json['is_completed'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'is_free': isFree,
      'content': content,
      'is_completed': isCompleted,
    };
  }

  @override
  String toString() {
    return 'Lesson{id: $id, title: $title, isFree: $isFree, isCompleted: $isCompleted, content: ${content.length} chars}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Lesson &&
        other.id == id &&
        other.title == title &&
        other.isFree == isFree &&
        other.content == content &&
        other.isCompleted == isCompleted;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        isFree.hashCode ^
        content.hashCode ^
        isCompleted.hashCode;
  }

  /// Create a copy of this lesson with updated completion status
  Lesson copyWith({
    int? id,
    String? title,
    bool? isFree,
    String? content,
    bool? isCompleted,
  }) {
    return Lesson(
      id: id ?? this.id,
      title: title ?? this.title,
      isFree: isFree ?? this.isFree,
      content: content ?? this.content,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}
