import 'package:flutter/foundation.dart';
import '../../../core/services/api_service.dart';
import '../../../core/services/storage_service.dart';
import '../models/lesson_model.dart';

class LessonProvider extends ChangeNotifier {
  final ApiService _apiService;
  final StorageService _storageService;

  LessonProvider(this._apiService, this._storageService);

  // Private fields
  List<Lesson> _lessons = [];
  bool _isLoading = false;
  String? _error;

  // Public getters
  List<Lesson> get lessons => _lessons;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Computed getters for convenience
  List<Lesson> get freeLessons =>
      _lessons.where((lesson) => lesson.isFree).toList();
  List<Lesson> get lockedLessons =>
      _lessons.where((lesson) => !lesson.isFree).toList();
  bool get hasLessons => _lessons.isNotEmpty;
  bool get hasError => _error != null;

  /// Load lessons from the API and update completion status from storage
  Future<void> loadLessons() async {
    _setLoading(true);
    _clearError();

    try {
      final fetchedLessons = await _apiService.fetchLessons();

      // Get completed lesson IDs from storage
      final completedLessonIds = _storageService.getCompletedLessons();

      // Update lessons with completion status
      _lessons = fetchedLessons.map((lesson) {
        final isCompleted = completedLessonIds.contains(lesson.id);
        return lesson.copyWith(isCompleted: isCompleted);
      }).toList();

      _clearError();
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _lessons = []; // Clear lessons on error
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh lessons (same as loadLessons but can be used for pull-to-refresh)
  Future<void> refreshLessons() async {
    await loadLessons();
  }

  /// Retry loading lessons after an error
  Future<void> retryLoadLessons() async {
    await loadLessons();
  }

  /// Mark a lesson as completed and update storage
  Future<void> markLessonCompleted(int lessonId) async {
    try {
      // Save to storage
      await _storageService.completeLesson(lessonId);

      // Update local lesson list
      _lessons = _lessons.map((lesson) {
        if (lesson.id == lessonId) {
          return lesson.copyWith(isCompleted: true);
        }
        return lesson;
      }).toList();

      notifyListeners();
    } catch (e) {
      print('Error marking lesson $lessonId as completed: $e');
      // Don't throw error to avoid breaking UI flow
    }
  }

  /// Get completion status for a specific lesson
  bool isLessonCompleted(int lessonId) {
    final lesson = _lessons.firstWhere(
      (lesson) => lesson.id == lessonId,
      orElse: () => Lesson(id: -1, title: '', isFree: false, content: ''),
    );
    return lesson.id != -1 ? lesson.isCompleted : false;
  }

  /// Get completed lessons count
  int get completedLessonsCount {
    return _lessons.where((lesson) => lesson.isCompleted).length;
  }

  /// Clear all data and reset to initial state
  void clearData() {
    _lessons = [];
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    // Clean up any resources if needed
    super.dispose();
  }
}
