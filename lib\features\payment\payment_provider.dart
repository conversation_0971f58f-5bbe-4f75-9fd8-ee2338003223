import 'package:flutter/material.dart';
import 'package:myket_iap/myket_iap.dart';
import '../../core/services/api_service.dart';
import '../../core/services/storage_service.dart';

/// Provider for managing Myket in-app purchase logic
/// Handles purchase flow, verification, and state management
class PaymentProvider extends ChangeNotifier {
  final ApiService _apiService;
  final StorageService _storageService;

  // Private state variables
  bool _isLoading = false;
  bool _isPurchased = false;

  // Replace with your actual product SKU
  static const String _productSku = 'YOUR_PRODUCT_SKU';

  // Constructor - requires ApiService and StorageService instances
  PaymentProvider(this._apiService, this._storageService) {
    // Initialize purchase state from storage
    _isPurchased = _storageService.getPurchaseState();
  }

  // Public getters
  bool get isLoading => _isLoading;
  bool get isPurchased => _isPurchased;

  /// Initialize Myket IAP
  /// Must be called once before using purchase functionality
  Future<void> init() async {
    try {
      // Initialize Myket IAP with null RSA key for server-side verification
      await MyketIAP.init(rsaKey: "", enableDebugLogging: true);
      print('Myket IAP initialized successfully');

      // Check for any unconsumed purchases from previous sessions
      await _checkUnconsumedPurchases();
    } catch (e) {
      print('Error initializing Myket IAP: $e');
      // Don't throw error to prevent app crash
    }
  }

  /// Check for unconsumed purchases from previous sessions
  Future<void> _checkUnconsumedPurchases() async {
    try {
      // Query inventory without SKU details for faster response
      Map<dynamic, dynamic> result =
          await MyketIAP.queryInventory(querySkuDetails: false);

      // Check if the query was successful
      dynamic queryResult = result[MyketIAP.RESULT];
      if (queryResult == null || !queryResult.isSuccess()) {
        print(
            'Failed to query inventory: ${queryResult?.message ?? "Unknown error"}');
        return;
      }

      // Get the inventory
      dynamic inventory = result[MyketIAP.INVENTORY];

      // Check if there's an unconsumed purchase for our product
      if (inventory != null && inventory.hasPurchase(_productSku)) {
        dynamic purchase = inventory.getPurchase(_productSku);
        if (purchase != null) {
          print('Found unconsumed purchase for SKU: $_productSku');

          // For now, we'll just consume it without verification
          // In a real app, you might want to verify with your server first
          await MyketIAP.consume(purchase: purchase);

          // Update local state
          _isPurchased = true;
          await _storageService.savePurchaseState(true);
          notifyListeners();

          print('Unconsumed purchase processed successfully');
        }
      }
    } catch (e) {
      print('Error checking unconsumed purchases: $e');
      // Don't throw error to prevent app crash
    }
  }

  /// Main purchase method
  /// Handles the complete purchase flow including verification and consumption
  Future<void> purchaseProduct(String mobileNumber) async {
    if (_isLoading) return; // Prevent multiple simultaneous purchases

    _setLoading(true);

    try {
      // Start the purchase flow
      Map<dynamic, dynamic> result =
          await MyketIAP.launchPurchaseFlow(sku: _productSku);

      // Get the purchase result and purchase object
      dynamic purchaseResult = result[MyketIAP.RESULT];
      dynamic purchase = result[MyketIAP.PURCHASE];

      if (purchaseResult != null &&
          purchaseResult.isSuccess() &&
          purchase != null) {
        print('Purchase successful, starting verification...');

        try {
          // Verify purchase with our backend server
          bool isVerified = await _apiService.verifyMyketPurchase(
            purchase.sku,
            purchase.purchaseToken,
            mobileNumber,
          );

          if (isVerified) {
            // Server verification successful, consume the purchase
            await MyketIAP.consume(purchase: purchase);

            // Update local state
            _isPurchased = true;
            await _storageService.savePurchaseState(true);

            print('Purchase verified and consumed successfully');
            _showSuccessMessage();
          } else {
            // Server verification failed
            print('Server verification failed for purchase');
            _showErrorMessage(
                'تایید خرید با سرور ناموفق بود. لطفاً با پشتیبانی تماس بگیرید.');
          }
        } catch (e) {
          // Error during verification
          print('Error during purchase verification: $e');
          _showErrorMessage('خطا در تایید خرید: ${e.toString()}');
        }
      } else {
        // Myket purchase failed
        String errorMessage = purchaseResult?.message ?? 'خطای نامشخص در خرید';
        print('Myket purchase failed: $errorMessage');
        _showErrorMessage('خرید ناموفق بود: $errorMessage');
      }
    } catch (e) {
      print('Error during purchase flow: $e');
      _showErrorMessage('خطا در فرآیند خرید: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Set loading state and notify listeners
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Show success message to user
  void _showSuccessMessage() {
    // This will be implemented when we have access to BuildContext
    // For now, just print
    print('خرید با موفقیت انجام شد!');
  }

  /// Show error message to user
  void _showErrorMessage(String message) {
    // This will be implemented when we have access to BuildContext
    // For now, just print
    print('خطا: $message');
  }

  /// Reset purchase state (for testing purposes)
  Future<void> resetPurchaseState() async {
    _isPurchased = false;
    await _storageService.savePurchaseState(false);
    notifyListeners();
  }
}
