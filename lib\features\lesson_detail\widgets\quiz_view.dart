import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_theme.dart';
import '../providers/quiz_provider.dart';
import 'quiz_result_view.dart';

class QuizView extends StatelessWidget {
  final int lessonId;
  final TabController? tabController;

  const QuizView({
    super.key,
    required this.lessonId,
    this.tabController,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<QuizProvider>(
      builder: (context, quizProvider, child) {
        if (quizProvider.isLoading) {
          return _buildLoadingState(context);
        }

        if (quizProvider.hasError) {
          return _buildErrorState(context, quizProvider);
        }

        if (!quizProvider.hasQuestions) {
          return _buildEmptyState(context);
        }

        if (quizProvider.quizCompleted) {
          return QuizResultView(
            quizProvider: quizProvider,
            tabController: tabController,
          );
        }

        return _buildQuizInterface(context, quizProvider);
      },
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppTheme.kPrimaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'در حال بارگذاری آزمون...',
            style: Theme.of(context).textTheme.bodyLarge,
            textDirection: TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, QuizProvider quizProvider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'خطا در بارگذاری آزمون',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 8),
            Text(
              quizProvider.error ?? 'خطای نامشخص',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => quizProvider.loadQuiz(lessonId),
              icon: const Icon(Icons.refresh),
              label: const Text(
                'تلاش مجدد',
                style: TextStyle(fontFamily: 'Samim'),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.kPrimaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.quiz_outlined,
              size: 64,
              color: Colors.grey[600],
            ),
            const SizedBox(height: 16),
            Text(
              'آزمونی برای این درس تعریف نشده است',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 8),
            Text(
              'در حال حاضر آزمونی برای این درس موجود نیست',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuizInterface(BuildContext context, QuizProvider quizProvider) {
    final currentQuestion = quizProvider.currentQuestion;
    if (currentQuestion == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Progress indicator
          _buildProgressIndicator(context, quizProvider),
          const SizedBox(height: 24),

          // Question
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildQuestionCard(context, currentQuestion, quizProvider),
                  const SizedBox(height: 24),
                  _buildOptionsSection(context, currentQuestion, quizProvider),
                ],
              ),
            ),
          ),

          // Navigation buttons
          const SizedBox(height: 16),
          _buildNavigationButtons(context, quizProvider),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(
      BuildContext context, QuizProvider quizProvider) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'سوال ${quizProvider.currentQuestionIndex + 1} از ${quizProvider.totalQuestions}',
              style: Theme.of(context).textTheme.titleMedium,
              textDirection: TextDirection.rtl,
            ),
            Text(
              '${(quizProvider.progressPercentage * 100).round()}%',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppTheme.kPrimaryColor,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: quizProvider.progressPercentage,
          backgroundColor: Colors.grey[700],
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.kPrimaryColor),
        ),
      ],
    );
  }

  Widget _buildQuestionCard(
      BuildContext context, question, QuizProvider quizProvider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Text(
          question.questionText,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                height: 1.5,
              ),
          textAlign: TextAlign.right,
          textDirection: TextDirection.rtl,
        ),
      ),
    );
  }

  Widget _buildOptionsSection(
      BuildContext context, question, QuizProvider quizProvider) {
    return Column(
      children: question.options.map<Widget>((option) {
        final isSelected =
            quizProvider.isAnswerSelected(question.id, option.id);

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Card(
            elevation: isSelected ? 6 : 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(
                color: isSelected ? AppTheme.kSuccessColor : Colors.transparent,
                width: 2,
              ),
            ),
            child: RadioListTile<int>(
              value: option.id,
              groupValue: quizProvider.getSelectedOption(question.id),
              onChanged: (value) {
                if (value != null) {
                  quizProvider.selectAnswer(question.id, value);
                }
              },
              title: Text(
                option.optionText,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: isSelected
                          ? AppTheme.kSuccessColor
                          : AppTheme.kTextColor,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                textDirection: TextDirection.rtl,
                textAlign: TextAlign.right,
              ),
              activeColor: AppTheme.kSuccessColor,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildNavigationButtons(
      BuildContext context, QuizProvider quizProvider) {
    return Row(
      children: [
        // Previous button
        if (!quizProvider.isFirstQuestion)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: quizProvider.previousQuestion,
              icon: const Icon(Icons.arrow_back_ios),
              label: const Text(
                'قبلی',
                style: TextStyle(fontFamily: 'Samim'),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[700],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),

        if (!quizProvider.isFirstQuestion) const SizedBox(width: 12),

        // Next/Submit button
        Expanded(
          flex: quizProvider.isFirstQuestion ? 1 : 1,
          child: ElevatedButton.icon(
            onPressed: quizProvider.canProceed
                ? () {
                    if (quizProvider.isLastQuestion) {
                      _showSubmitDialog(context, quizProvider);
                    } else {
                      quizProvider.nextQuestion();
                    }
                  }
                : null,
            icon: Icon(
              quizProvider.isLastQuestion
                  ? Icons.check
                  : Icons.arrow_forward_ios,
            ),
            label: Text(
              quizProvider.isLastQuestion ? 'ارسال آزمون' : 'بعدی',
              style: const TextStyle(fontFamily: 'Samim'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.kPrimaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  void _showSubmitDialog(BuildContext context, QuizProvider quizProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'ارسال آزمون',
            style: TextStyle(fontFamily: 'Samim'),
            textDirection: TextDirection.rtl,
          ),
          content: const Text(
            'آیا مطمئن هستید که می‌خواهید آزمون را ارسال کنید؟',
            style: TextStyle(fontFamily: 'Samim'),
            textDirection: TextDirection.rtl,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'انصراف',
                style: TextStyle(fontFamily: 'Samim'),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                quizProvider.submitQuiz(lessonId);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.kPrimaryColor,
              ),
              child: const Text(
                'ارسال',
                style: TextStyle(fontFamily: 'Samim', color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }
}
