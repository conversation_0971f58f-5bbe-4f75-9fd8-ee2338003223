import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/theme/app_theme.dart';
import 'providers/lesson_provider.dart';
import 'widgets/lesson_list_item.dart';
import 'widgets/purchase_button.dart';
import '../auth/auth_screen.dart';
import '../payment/payment_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Load lessons and initialize payment provider after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<LessonProvider>(context, listen: false).loadLessons();
      Provider.of<PaymentProvider>(context, listen: false).init();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Text(
          'نویسنده شو',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
          textDirection: TextDirection.rtl,
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(
              Icons.login,
              color: Colors.white,
            ),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AuthScreen(),
                ),
              );
            },
          ),
        ],
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: AppGradients.appBarBackground,
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppGradients.mainBackground,
        ),
        child: SafeArea(
          child: Consumer<LessonProvider>(
            builder: (context, lessonProvider, child) {
              return _buildBody(lessonProvider);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildBody(LessonProvider lessonProvider) {
    if (lessonProvider.isLoading) {
      return _buildLoadingState();
    }

    if (lessonProvider.hasError) {
      return _buildErrorState(lessonProvider);
    }

    if (!lessonProvider.hasLessons) {
      return _buildEmptyState();
    }

    return _buildLessonsList(lessonProvider);
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: Colors.tealAccent[400],
          ),
          const SizedBox(height: 16),
          Text(
            'در حال بارگذاری درس‌ها...',
            style: Theme.of(context).textTheme.bodyLarge,
            textDirection: TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(LessonProvider lessonProvider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'خطا در بارگذاری درس‌ها',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 8),
            Text(
              lessonProvider.error ?? 'خطای نامشخص',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => lessonProvider.retryLoadLessons(),
              icon: const Icon(Icons.refresh),
              label: const Text(
                'تلاش مجدد',
                style: TextStyle(fontFamily: 'Samim'),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.tealAccent[400],
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.school_outlined,
              size: 64,
              color: Colors.grey[600],
            ),
            const SizedBox(height: 16),
            Text(
              'هیچ درسی یافت نشد',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 8),
            Text(
              'در حال حاضر درسی برای نمایش وجود ندارد',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLessonsList(LessonProvider lessonProvider) {
    return RefreshIndicator(
      onRefresh: () => lessonProvider.refreshLessons(),
      color: Colors.tealAccent[400],
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 16),
        itemCount: lessonProvider.lessons.length + 1, // +1 for purchase button
        itemBuilder: (context, index) {
          // Show purchase button as first item
          if (index == 0) {
            return const Padding(
              padding: EdgeInsets.only(bottom: 16),
              child: PurchaseButton(),
            );
          }

          // Show lessons (adjust index by -1)
          final lesson = lessonProvider.lessons[index - 1];
          return LessonListItem(
            lesson: lesson,
          );
        },
      ),
    );
  }
}
